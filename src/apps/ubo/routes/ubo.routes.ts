import { type RouteConfig } from 'vue-router';

import SidebarMenuLayout from '@/shared/layouts/sidebar-menu';
import { Permission } from '@/config/permissions.config';

const APP_MENU_CONFIG = [
  {
    collapse: true,
    icon: 'icon-indent-decrease',
  },
  {
    border: true,
  },
  {
    key: 'ubo',
    icon: 'icon-xiaoxizhongxin',
    label: '受益人识别',
    children: [
      {
        key: '/ubo/identify-single',
        label: '单一识别',
      },
      {
        key: '/ubo/identify-batch',
        label: '批量识别',
      },
    ],
  },
];

export const uboRoutes = (): RouteConfig[] => [
  {
    path: '/ubo',
    component: SidebarMenuLayout,
    props: {
      pageTitle: '受益人识别',
      menu: APP_MENU_CONFIG,
    },
    meta: {
      title: '受益人识别',
    },
    redirect: () => {
      return {
        name: 'ubo-identify-single',
      }
    },
    children: [
      {
        name: 'ubo-identify-single',
        path: '/ubo/identify-single',
        component: () => import('../pages/identify-single'),
        meta: {
          title: '单一识别',
          permission: [Permission.INVESTIGATION_VIEW],
        },
      },
      {
        name: 'ubo-identify-batch',
        path: '/ubo/identify-batch',
        component: () => import('../pages/identify-batch'),
        meta: {
          title: '批量识别',
          permission: [Permission.INVESTIGATION_VIEW],
        },
      },
    ],
  },
];
