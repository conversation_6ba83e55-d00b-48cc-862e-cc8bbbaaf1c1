import { findPathNodes } from './utils';
import testdata from './data3.json';
import { forEach } from 'lodash';

// TODO mock
const mockPost = (path, params) => {
  return Promise.resolve(testdata);
};

const getCommonReqService = (isCommonReq, { keyNo, name, percent, mode, from, fromCorpKeyNo, functionTableId }) => {
  if (isCommonReq) {
    return mockPost('/corp/common/businessInfo/forceChartBeneBreakThroughForCorpDetail', {
      condition: {
        rootCorpKeyNo: keyNo,
        rootCorpName: name,
      },
    });
  }
  return mockPost('/beneficary/forceChartBeneBreakThrough', {
    condition: {
      rootCorpKeyNo: keyNo,
      rootCorpName: name,
      percent: percent || 25,
      // isHistory: '',
      type: 'U',
      isCorp: mode, // 穿透方式： 0-自然人；1-企业；2-所有
      from: from || '',
      corpType: 'C',
      fromCorpKeyNo: fromCorpKeyNo || '',
      functionTableId: functionTableId || '', // 空值  或者 ubo_id
    },
  });
};

export const loadUboStructureData = (
  { keyNo, name, percent, mode, from, fromCorpKeyNo, functionTableId },
  includeNodeList = undefined,
  isCommonReq = false
) => {
  return new Promise((resolve, reject) => {
    getCommonReqService(isCommonReq, {
      keyNo,
      name,
      percent,
      mode,
      from,
      fromCorpKeyNo,
      functionTableId,
    })
      .then((res) => {
        const parseData = res?.resultList?.[0]?.data?.[0]?.graph || {};
        resolve(handleDetailWithListed(parseData, keyNo, name, includeNodeList));
      })
      .catch((err) => {
        console.error(err);
        reject(err);
      });
  });
};
export const handleDetailWithListed = (parseData, eid, ename, includeNodeList) => {
  const data = parseData || {};
  const links = [];
  const nodes = [];
  if (includeNodeList?.length === 2) {
    data.nodes = findPathNodes(data.nodes, data.relationships, includeNodeList[0], includeNodeList[1]);
  }
  forEach(data.nodes, (node, key) => {
    nodes.push({
      nodeId: node.id,
      keyNo: node.properties?.keyNo || '',
      name: node.properties?.name || '',
      uuid: node.id,
      isRoot: eid === (node.properties?.keyNo || ''),
      level: node.properties?.depth || 0,
      xsort: 0,
    });
  });
  forEach(data.relationships, (link, key) => {
    links.push({
      linkId: link.id,
      source: link.startNode,
      target: link.endNode,
      name: '',
      lineTextPrefix: link?.properties?.shouldCapi || '',
      lineTextSuffix: link?.properties?.shouldCapi || '',
      isSpecial: true,
      percent: link?.properties?.stockPercent || '',
      lineText: (link?.properties?.stockPercent && `${link?.properties?.stockPercent}%`) || '',
    });
  });
  formatNodeListData(nodes, links);
  return {
    name: data.CompanyName,
    keyNo: eid,
    nodes,
    links,
  };
};

//  格式化节点列表数据，找出开始节点并标记开始节点
const formatNodeListData = (nodeList, links) => {
  forEach(nodeList, (node, key) => {
    const findOne = links.some((v) => v.target === node.nodeId);
    node.isTopNode = !findOne;
  });
};
