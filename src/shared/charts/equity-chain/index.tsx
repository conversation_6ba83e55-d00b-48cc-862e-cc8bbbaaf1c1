import { defineComponent, onMounted, PropType, reactive, ref } from 'vue';

import { exportImage } from '@/utils/yfiles/lib/image-export';
import { NodeType } from '@/utils/yfiles/lib/node-type';
import { clearHighlights, handleHoverClass } from '@/utils/yfiles/lib/NodeHighlightManager';
import { calcPosition, getNodeSize } from '@/utils/yfiles/templates/helper';
import { buildGraph, createLayoutConfig, createLayoutData } from './graph';
import '@/utils/yfiles/license-loader/license-loader';
import { GraphItemTypes, Insets, ScrollBarVisibility } from 'yfiles';
import { startsWith } from 'lodash';
import { loadUboStructureData } from './data';
import { isFakeKeyNo } from '@/utils';

const EquityChain = defineComponent({
  name: 'EquityChain',
  props: {
    containerId: {
      type: String,
      default: 'equity-chain',
    },
    companyInfo: {
      type: Object as PropType<{ name: string; keyNo: string }>,
      default: () => ({ name: '乐视网信息技术（北京）股份有限公司', keyNo: '84c17a005a759a5e0d875c1ebb6c9846' }),
    },
    // 传入该值时，将会过滤列表，将指定的内容才会显示在图上，参考样例： ['雷军', '小米科技有限公司']
    nodeKeyNoList: {
      type: Array as PropType<string[]>,
      required: false,
    },
  },
  setup(props, { emit }) {
    const graphComponent = ref();
    const dataSource = ref<{ nodes: any; links: any }>({ links: [], nodes: [] });
    const scale = ref<number>(100);

    const chartData = reactive<{ nodes: any; edges: any }>({
      nodes: [],
      edges: [],
    });

    const clearHover = () => {
      //
    };

    const handleNodeHover = ({ event }) => {
      const hoverItem = event.item;
      console.log('🚀 ~ handleNodeHover ~ hoverItem:', hoverItem);

      if (!hoverItem) {
        clearHover();
      }

      /** 置灰的节点不展示popover */
      if (hoverItem?.style?.cssClass === 'gray20') {
        return;
      }

      const data = dataSource.value.nodes.find((item) => item.keyNo === hoverItem?.tag?.unique);

      if (!data) {
        clearHover();
        return;
      }

      // this.hoverData = data

      const size = null;
      if (!data?.keyNo || data.org < 0) {
        // -1 无效企业 -2无效的人
        /** combined类型，全部存在keyNo，不展示复制 */
        let combinedAllNoKey = false;
        if (hoverItem?.tag?.combinedNameList?.length) {
          const noKeyNo = hoverItem?.tag?.combinedNameList?.find((i) => !i.keyNo);
          combinedAllNoKey = !noKeyNo;
        }
        if (combinedAllNoKey) {
          clearHover();
          return;
        }

        // popoverHelper.showDetailCard({
        //   component: appCopy,
        //   data: {
        //     text: data.name,
        //     isVertical: false,
        //   },
        //   direction: 'bottom',
        //   directionX: 'left',
        //   size,
        //   container: $(`#${this.containerId}`),
        //   identity: `detail-popover-${data.nodeId}`,
        //   callback: this.moveCardPosition(event.item, 'copy'),
        //   isYFile: true,
        // });

        return;
      }

      if (isFakeKeyNo(data.keyNo)) {
        return;
      }

      // popoverHelper.showDetailCard({
      //   component: data.keyNo && data.keyNo[0] === 'p' ? appPerson : appCompany,
      //   data: {
      //     hasKeyNo: true,
      //     id: data.keyNo,
      //     name: data.name,
      //     keyNo: data.keyNo,
      //     rsTags: data.rsTags,
      //     eid: this.companyKeyNo,
      //     ename: this.companyName,
      //     org: data.org,
      //     hideEmptyDataInPath: true,
      //   },
      //   container: $(`#${this.containerId}`).parent(),
      //   size,
      //   identity: `detail-${data.nodeId}`,
      //   callback: this.moveCardPosition(event.item, 'card'),
      //   isYFile: true,
      // });
    };

    const initializeGraphComponent = async () => {
      const { graphComponent, graphBuilder, nodeSource, edgeSource, graphInputMode } = buildGraph(`${props.containerId}`);

      // 设置最大缩放比例为 2.5
      graphComponent.maximumZoom = 2.5;
      // 设置最小缩放比例为 0.4
      graphComponent.minimumZoom = 0.4;

      graphComponent.focusIndicatorManager.enabled = false;
      graphComponent.selectionIndicatorManager.enabled = false;

      chartData.nodes = [];
      chartData.edges = [];
      // 数据处理
      dataSource.value.nodes.forEach((node) => {
        const combinedHeight = 0;

        const areaTag = node.tags?.length
          ? {
              name: node.tags[0].name,
            }
          : { name: '' };
        const nodeType = node.listedAc?.length > 1 ? NodeType.combined : startsWith(node.keyNo, 'p') ? NodeType.person : NodeType.company;
        const { isOversize, layoutSize, nodeSize, labelSize, tagSize } = getNodeSize({
          nodeType,
          text: node.name,
          labels: node.rsTags,
          tag: areaTag.name,
          fixedHeight: combinedHeight,
          isBold: node.isRoot,
        });

        const preparedNode = {
          id: node.nodeId,
          isTopNode: node.isTopNode,
          name: node.name,
          unique: node.keyNo,
          isSelf: node.isRoot,
          type: nodeType,
          areaTag,
          tipLabels: node?.rsTags,
          imageUrl: node.image ? node.image : '',
          level: node.level,
          isOversize,
          combinedHeight,
          layoutSize,
          nodeSize,
          labelSize,
          tagSize,
        };

        chartData.nodes.push(preparedNode);
      });

      dataSource.value.links.forEach((link) => {
        chartData.edges.push({
          id: link.linkId,
          sourceId: link.source,
          targetId: link.target,
          lineType: link.lineType,
          lineText: link.lineText,
          isMain: link.isRed,
          tips: link.tips,
        });
      });
      graphBuilder.setData(nodeSource, chartData.nodes);
      graphBuilder.setData(edgeSource, chartData.edges);

      graphComponent.fitGraphBounds();

      graphComponent.addZoomChangedListener(() => {
        const value = Number(graphComponent.zoom?.toFixed(2));
        scale.value = value;
        scale.value = Number((value * 10).toFixed(0)) * 10;
      });

      graphComponent.verticalScrollBarPolicy = ScrollBarVisibility.NEVER;
      graphComponent.horizontalScrollBarPolicy = ScrollBarVisibility.NEVER;
      graphComponent.addMouseDragListener((_sender, evt) => {
        clearHover();
      });

      graphBuilder.updateGraph();

      graphComponent.fitGraphBounds();
      // 更新布局
      await graphComponent.morphLayout({
        targetBoundsInsets: Insets.from([0, 0, 0, 0]),
        layout: createLayoutConfig(),
        layoutData: createLayoutData(graphComponent),
        morphDuration: '0.3s',
      });
      graphInputMode.addItemClickedListener((_sender, evt) => {
        const tag = evt.item.tag;
        if (tag?.unique) {
          const entity = {
            id: tag.unique,
            eid: tag.unique,
            ename: tag.name,
            org: '',
            pathData: null,
          };
          emit('showDetail', { data: entity });
        }
      });

      graphInputMode.addCanvasClickedListener((_sender, evt) => {
        emit('clickEmpty');
      });

      graphInputMode.itemHoverInputMode.hoverItems = GraphItemTypes.NODE;
      // 定义一个变量来保存定时器的ID
      let timerId: number | null = null;
      graphInputMode.itemHoverInputMode.addHoveredItemChangedListener((_sender, evt) => {
        /** 处理hover效果 */
        handleHoverClass(evt, graphComponent.value, (row) => {
          if (row.type === 'person' && row.isTopNode) {
            return 'top-node ';
          } else if (row.type === 'company' && row.isTopNode) {
            return 'top-node ';
          }
          return '';
        });

        if (evt.item) {
          // 如果鼠标悬停在节点上，则设置一个定时器，在500毫秒后触发
          timerId = setTimeout(() => {
            handleNodeHover({ event: evt });
            timerId && clearTimeout(timerId); // 清除定时器
            timerId = null; // 重置定时器ID
          }, 200);
        } else {
          // 如果鼠标离开节点，则清除定时器
          timerId && clearTimeout(timerId);
          timerId = null;
          clearHover();
        }
      });

      graphComponent.value = graphComponent;
    };

    const initData = () => {
      return new Promise((resolve, reject) => {
        // this.isInit = false
        // this.noData = false
        loadUboStructureData(
          {
            // keyNo: this.keyNo,
            // name: this.companyName,
            // percent: this.percent,
            // mode: this.options.mode || 0,
            // from: this.options.from || '',
            // fromCorpKeyNo: this.options.fromCorpKeyNo || '',
            // functionTableId: this.options.functionTableId || '',
          },
          props.nodeKeyNoList
          // this.$route.name === 'breakthrough-structure-chart'
        )
          .then((data) => {
            // this.isLoading = false;
            // this.paths = data.paths
            // this.data = data
            dataSource.value = data;
            emit('loadDataSuccess', {
              companyName: props.companyInfo.name,
              companyKeyNo: props.companyInfo.keyNo,
            });
            resolve({});
          })
          .catch((err) => {
            // this.isInit = true;
            // this.noData = true;
            reject(err);
          });
      });
    };

    onMounted(async () => {
      await initData();
      initializeGraphComponent();
    });

    return {
      graphComponent,
    };
  },
  render() {
    return <div id={this.containerId} class="chart-container-body" style={{ height: '500px' }}></div>;
  },
});

export default EquityChain;
