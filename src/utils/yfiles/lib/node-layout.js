/*
 * Created by chen x<PERSON> song on - 2023/11/28.
 */

// let yfiles

// if (__BROWSER__) {
//   yfiles = require('yfiles')
// }

import { Rect } from 'yfiles'

/** 通用公司布局大小 */
export const nodeCompanyLayout = ({
  graphComponent,
  layoutSize
}) => {
  return new Rect(
    (graphComponent.size.width - layoutSize.width) / 2,
    (graphComponent.size.height - layoutSize.height) / 2,
    layoutSize.width,
    layoutSize.height
  )
}

/** 通用人员布局大小 */
export const nodePersonLayout = ({
  graphComponent,
  layoutSize
}) => {
  if (layoutSize?.height) {
    return new Rect(
      (graphComponent.size.width - layoutSize.width) / 2,
      (graphComponent.size.height - layoutSize.height) / 2,
      layoutSize.width,
      layoutSize.height
    )
  }
  return new Rect(
    (graphComponent.size.width - layoutSize.width) / 2,
    (graphComponent.size.height - layoutSize.height) / 2,
    layoutSize.width,
    layoutSize.height
  )
}

/** 超长人员定宽纵向适配布局 */
export const nodePersonOverSizeLayout = ({
  graphComponent,
  layoutSize
}) => {
  return new Rect(
    (graphComponent.size.width - layoutSize.width) / 2,
    (graphComponent.size.height - layoutSize.height) / 2,
    layoutSize.width,
    layoutSize.height
  )
}
