import _ from 'lodash';

import shuiyingImage from '@/assets/images/shuiying6.png';
// import downloadUtils from '../../download-img'
import { companyNodeSize, personNodeSize } from '../lib/node-size';
import { NodeType } from '../lib/node-type';

// let yfiles

// if (__BROWSER__) {
//   yfiles = require('yfiles')
// }

import { Font, Point, Size, TextMeasurePolicy, TextRenderSupport, TextWrapping } from 'yfiles';

export const SVGNS = 'http://www.w3.org/2000/svg';

export const createText = ({ text, fontColor = '#F6FBFE', fontSize = '12', fontFamily = '', fontWeight = '' }) => {
  const textEl = document.createElementNS(SVGNS, 'text');

  textEl.textContent = text;
  if (fontFamily) {
    textEl.setAttribute('font-family', fontFamily);
  }

  if (fontWeight) {
    textEl.setAttribute('font-weight', fontWeight);
  }

  textEl.setAttribute('font-size', fontSize);
  textEl.setAttribute('fill', fontColor);

  return textEl;
};

export const addText = (name, targetElement) => {
  const fontInfo = { fontSize: 12 };

  TextRenderSupport.addText({
    targetElement,
    text: name,
    font: new Font(fontInfo),
    maximumSize: new Size(companyNodeSize.width, Number.POSITIVE_INFINITY),
    wrapping: TextWrapping.WORD,
    measurePolicy: TextMeasurePolicy.AUTOMATIC,
  });
};

const getNodeTipHeight = (labels) => {
  // 水平padding 10 垂直padding 5
  const hGap = 10;
  const vGap = 5;

  if (!labels.length) {
    return { width: 0, height: 0 };
  }
  const maxText = _.maxBy(labels, (o) => o.length);
  const maxLabelSize = TextRenderSupport.measureText(maxText, new Font({ fontSize: 12 }));
  const tagWidth = Math.max(maxLabelSize.width + hGap * 2, 128);
  // 字体大小12 行高16 三角形5
  const tagHeight = labels.length * 12 + (labels.length - 1) * 4 + vGap * 2;
  const size = { width: tagWidth, height: tagHeight };
  return size;
};

// 实际控制人 标签
export const createControlTag = ({ size = { width: 128, height: 38 }, labels = [] } = {}) => {
  const tipSize = getNodeTipHeight(labels);
  const tagWidth = tipSize.width;
  const tagHeight = tipSize.height;

  const redColor = '#F04040';
  let fillColor = labels?.length > 1 ? redColor : '#128bed';
  if (_.includes(labels, '实际控制人')) {
    fillColor = redColor;
  }

  const tagGroup = document.createElementNS(SVGNS, 'g');
  const rectEl = document.createElementNS(SVGNS, 'rect');

  rectEl.setAttribute('fill', fillColor);
  rectEl.setAttribute('width', String(tagWidth));
  rectEl.setAttribute('height', String(tagHeight));
  rectEl.setAttribute('rx', '2');
  tagGroup.appendChild(rectEl);

  const reactArrow = document.createElementNS(SVGNS, 'polygon');

  // 三角形 高度5 宽度10
  const p1x = tagWidth / 2 - 5;
  const p1y = tagHeight - 1;
  const p2x = p1x + 10;
  const p2y = tagHeight - 1;
  const p3x = tagWidth / 2;
  const p3y = tagHeight + 4;
  reactArrow.setAttribute('points', `${p1x},${p1y} ${p2x},${p2y} ${p3x},${p3y}`);
  reactArrow.setAttribute('fill', fillColor);

  tagGroup.appendChild(reactArrow);

  const paddingV = (tagHeight - labels.length * 16) / 2;
  const font = new Font({ fontSize: 12 });

  labels.forEach((labelText, idx) => {
    const tagElem = createText({ text: labelText, fontSize: '12' });
    const labelSize = TextRenderSupport.measureText(labelText, font);
    tagElem.setAttribute('x', String((tagWidth - labelSize.width) / 2));
    tagElem.setAttribute('y', String(-4 + paddingV + (idx + 1) * 16));
    tagElem.setAttribute('font-weight', 'bold');
    tagGroup.appendChild(tagElem);
  });

  return { controlTag: tagGroup, tipSize };
};

export const getKzrTipHeight = (labels) => {
  const hGap = 10;
  const vGap = 5;
  if (!labels.length) {
    return { width: 0, height: 0 };
  }
  const maxText = _.maxBy(labels, (o) => o.length);
  const font = new Font({ fontSize: 12 });
  const maxLabelSize = TextRenderSupport.measureText(maxText, font);
  const tagWidth = Math.max(maxLabelSize.width + hGap * 2, 128);
  // 字体大小12 行高16
  const tagHeight = labels.length * 12 + (labels.length - 1) * 4 + vGap * 2;
  const size = { width: tagWidth, height: tagHeight };
  return size;
};

const measureTextOversize = ({ name = '', nodeType = NodeType.company, isBold = false }) => {
  let labelSize = new Size(0, 0);
  let isOversize = false;
  let nodeSize = { width: 0, height: 0 };
  if (nodeType === NodeType.company) {
    const textRender = {
      text: name,
      font: new Font({ fontSize: 12, fontWeight: isBold ? 'bold' : 'normal' }),
      wrapping: TextWrapping.WORD,
      maximumSize: new Size(companyNodeSize.width, Infinity),
    };
    labelSize = TextRenderSupport.measureText(textRender);
    isOversize = labelSize.height + 10 >= companyNodeSize.height;
    // 公司节点限制高度不低于42
    const nodeHeight = Math.max(companyNodeSize.height, labelSize.height + 10);
    nodeSize = { width: companyNodeSize.width, height: nodeHeight };
  } else if (nodeType === NodeType.person) {
    const textRender = {
      text: name,
      font: new Font({ fontSize: 12 }),
      wrapping: TextWrapping.NONE,
      maximumSize: new Size(personNodeSize.width, Infinity),
    };

    labelSize = TextRenderSupport.measureText(textRender);
    isOversize = labelSize.width > personNodeSize.width - 5;

    if (isOversize) {
      // 实际渲染的时候 文字不能和框一样大 两边各保留5
      textRender.maximumSize = new Size(companyNodeSize.width - 10, Infinity);
      textRender.wrapping = TextWrapping.WORD;
      labelSize = TextRenderSupport.measureText(textRender);
      // 人员名称超宽的情况下 为避免椭圆弧度过于尖锐 限制高度不能低于42
      const labelHeight = Math.max(labelSize.height + 10, companyNodeSize.height);
      nodeSize = new Size(companyNodeSize.width, labelHeight);
    } else {
      nodeSize = new Size(personNodeSize.width, personNodeSize.height);
    }
  }
  return {
    isOversize,
    labelSize,
    nodeSize,
  };
};

export const getNodeSize = ({
  text, // 节点文案
  nodeType, // 节点类型
  labels = [], // 节点顶部tip文案
  tag = '', // 节点↗右上标签数据
  fixedHeight = 0,
  statusTag = '', // 节点正下状态数据
  publicStatusTextList = [], // 节点公开状态文案
  publicStatusGWidth = [], // 节点公开状态宽度
  isBold = false,
}) => {
  const { isOversize, labelSize, nodeSize } = measureTextOversize({ name: text, nodeType, isBold });

  // 顶部tip的大小 例如：实际控制人
  let tipSize = { width: 0, height: 0 };
  // 标签tag的大小 地区标签
  let tagSize = { width: 0, height: 0 };
  /** 节点底部金融信息 */
  let publicSize = { width: publicStatusGWidth, height: 0 };

  // 节点整体布局的大小 包含了 tip tag label
  const layoutSize = { width: nodeSize.width, height: nodeSize.height };

  // 计算顶部控制人标签高度
  if (labels?.length) {
    tipSize = getNodeTipHeight(labels);
    layoutSize.width = tipSize.width;
    layoutSize.height += tipSize.height + 10;
  }

  // 区域标签高度
  if (tag) {
    tagSize = TextRenderSupport.measureText(tag, new Font({ fontSize: 10 }));
    layoutSize.height += (tagSize.height / 3) * 2;
  }

  if (publicStatusTextList.length) {
    const publicHeight = 18 * publicStatusTextList.length + 4;
    layoutSize.height += publicHeight;
    publicSize.height = publicHeight;
  } else if (statusTag) {
    // 目前状态标签，定高22 根据内容行数撑开高度
    layoutSize.height += 22;
    const singleSize = TextRenderSupport.measureText(statusTag, new Font({ fontSize: 10 }));
    publicSize = { height: 22, width: singleSize.width };
  }

  // combined 的情况下 节点高度是通过预渲染html元素获取的
  if (fixedHeight) {
    layoutSize.width = companyNodeSize.width;
    layoutSize.height = fixedHeight;
  }
  return { isOversize, layoutSize, nodeSize, tipSize, tagSize, labelSize, publicSize };
};

export const getImgUrlData = (imgdata, companyName, noNeedWaterMakers = false, trackObj = null) => {
  const newConsole = console;
  const img = new Image();
  img.crossOrigin = 'Anonymous';
  img.src = imgdata;
  const shuiying = new Image();
  shuiying.crossOrigin = 'Anonymous';
  shuiying.src = shuiyingImage;
  const loading = (image) => {
    return new Promise((resolve, reject) => {
      image.onload = function () {
        resolve(image);
      };
      image.onerror = function (e) {
        newConsole.log('img-error', e);
        reject(e);
      };
    });
  };

  return Promise.all([loading(img), loading(shuiying)])
    .then((res) => {
      const canvas = document.createElement('canvas'); // 准备空画布
      const minSize = { w: 800, height: 600 };
      canvas.width = _.max([minSize.w, img.width + 100]);
      canvas.height = _.max([minSize.h, img.height + 200]);
      const context = canvas.getContext('2d'); // 取得画布的2d绘图上下文
      context.fillStyle = '#FFFFFF';
      context.fillRect(0, 0, canvas.width, canvas.height);
      if (!noNeedWaterMakers) {
        for (let i = 0; i < canvas.width + shuiying.width / 2; i += shuiying.width / 2) {
          for (let j = 0; j < canvas.height + shuiying.height / 2; j += shuiying.height / 2) {
            context.drawImage(shuiying, i, j, shuiying.width / 2, shuiying.height / 2);
          }
        }
      }
      // 画图谱
      context.drawImage(img, (canvas.width - img.width) / 2, (canvas.height - img.height) / 2);
      context.font = '16px 微软雅黑';
      context.fillStyle = '#999999';
      const dataUri = canvas.toDataURL();
      if (trackObj) {
        trackObj.data.size = {
          height: canvas.height,
          width: canvas.width,
        };
      }
      return dataUri;
    })
    .catch((e) => {
      newConsole.log('getImgUrlData-error', e);
    });
};

/** 新版下载逻辑 */
export const getImgUrlDataNew = ({ scale, imgdata, companyName, title, noWatermark = false }) => {
  // return downloadUtils.saveForGraph({ scale, imgData: imgdata, companyName, title, noWatermark, type: 'uri' })
};

export const createTooltipContent = (item) => {
  const label = item?.owner?.tag?.tips;
  const tooltip = document.createElement('div');
  tooltip.innerHTML = label;
  return tooltip;
};

// 计算hover元素位置
export const calcPosition = ({ nodeItem, graphComponent, useBottom, containerWidth, needAdjustHeight = false, containerHeight }) => {
  let point = nodeItem.layout.topRight;
  if (useBottom) {
    point = nodeItem.layout.bottomRight;
  }
  let isLeft = false;
  let viewPoint = graphComponent.toViewCoordinates(new Point(point.x, point.y));
  // 计算卡片是否显示到了外部
  const outerWidth = $('.detail-card').outerWidth();
  const outerHeight = $('.detail-card').outerHeight();
  const judgeWidth = containerWidth ? containerWidth : document.body.clientWidth;
  let isTop = false;
  if (needAdjustHeight && viewPoint.y + outerHeight > containerHeight) {
    isTop = true;
  }
  if (viewPoint.x + outerWidth > judgeWidth) {
    point = nodeItem.layout.topLeft;
    if (useBottom) {
      point = nodeItem.layout.bottomLeft;
    }
    isLeft = true;
    viewPoint = graphComponent.toViewCoordinates(new Point(point.x, point.y));
    if (isTop) {
      return { x: viewPoint.x - outerWidth, y: viewPoint.y - outerHeight + nodeItem.layout.height, isLeft, outerWidth, outerHeight };
    }
    return { x: viewPoint.x - outerWidth, y: viewPoint.y, isLeft, outerWidth, outerHeight };
  }
  if (isTop) {
    return { x: viewPoint.x, y: viewPoint.y - outerHeight + nodeItem.layout.height, isLeft, outerWidth, outerHeight };
  }
  return { x: viewPoint.x, y: viewPoint.y, isLeft, outerWidth, outerHeight };
};

export const getOneRowLabelWidth = (text, font) => {
  const size = TextRenderSupport.measureText(text, font);
  return size.width;
};
