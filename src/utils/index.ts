import { cloneDeep, isElement } from 'lodash';

export * from './http-client';
export * from './uuid';
export * from './transform';

// 复制
export const copyToClipboard = (text) => {
  const textarea = document.createElement('textarea');
  textarea.value = text;
  document.body.appendChild(textarea);
  textarea.select();
  document.execCommand('Copy');
  textarea.remove();
};

// 从html标签中获取文本
export const getHTMLText = (html) => {
  const div = document.createElement('div');
  div.innerHTML = html;
  return div.textContent || div.innerText;
};

// 更改网站图标
export const changeFavicon = (iconUrl: string) => {
  const head = document.getElementsByTagName('head')[0];
  const existingIcons = head.querySelectorAll('link[rel="icon"]');

  // Remove any existing favicon links
  for (let i = 0; i < existingIcons.length; i++) {
    head.removeChild(existingIcons[i]);
  }

  // Create a new link element for the new icon
  const newIcon = document.createElement('link');
  newIcon.rel = 'icon';
  if (iconUrl?.split('.')[-1] === 'svg') {
    newIcon.type = 'image/svg+xml';
  }
  newIcon.type = 'image/x-icon';
  newIcon.href = iconUrl;

  // Add the new link element to the head
  head.appendChild(newIcon);
};

// 判断是否文本溢出
export const isTextOverflow = (element: Element) => {
  if (!isElement(element)) {
    return false;
  }
  return element.scrollWidth > element.clientWidth;
};

export const getOptionsByMap = (map: Record<string, any>) => {
  return Object.entries(map).map(([value, label]) => ({
    value,
    label,
  }));
};

export const sleep = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

export function stringifyObjectValues(payload: Record<string, any>): Record<string, string> {
  return Object.keys(payload).reduce((acc, cur) => {
    return {
      ...acc,
      [cur]: JSON.stringify(payload[cur]),
    };
  }, {});
}

export const calcRowSpan = (data, mergeKey: string) => {
  if (!data || data.length === 0) {
    return [];
  }
  const cloneData = cloneDeep(data);
  let key = cloneData[0][mergeKey];
  let rowIndex = 0;
  cloneData.forEach((item, index, arr) => {
    if (item[mergeKey] === key) {
      item.attrs = { rowSpan: 0 };
    } else {
      arr[rowIndex].attrs = { rowSpan: index - rowIndex };
      rowIndex = index;
      key = item[mergeKey];
    }
  });
  cloneData[rowIndex].attrs = { rowSpan: cloneData.length - rowIndex };
  return cloneData;
};

export const getOssUrl = (suffix: string) => {
  if (!suffix) return;
  if (!suffix.startsWith('/')) {
    return `//qccdata.qichacha.com/${suffix}`;
  }
  return `//qccdata.qichacha.com${suffix}`;
};

export const getArea = (area = {} as { ProvinceName: string; CityName: string; CountyName: string }): string => {
  const { ProvinceName, CityName, CountyName } = area;
  const res: string[] = [];
  if (ProvinceName) {
    res.push(ProvinceName);
  }
  if (CityName && ProvinceName !== CityName) {
    res.push(CityName);
  }
  if (CountyName) {
    res.push(CountyName);
  }
  return res.join('');
};

export const isFakeKeyNo = (keyNo) => {
  const startStr = ['pz', 'cr', 'rp'];
  const startKeyNo = keyNo.substring(0, 2);
  return startStr.includes(startKeyNo);
};
